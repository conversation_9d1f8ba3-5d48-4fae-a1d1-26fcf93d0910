# Mobile GT+ Handover Documentation

## Project Overview
Mobile GT+ is a Flutter-based application that focuses on PPG (Photoplethysmography) recording, voice recording, face mesh detection, and data upload operations to Google Cloud Storage.

## 1. Database Operations

### Firestore Collections Structure

#### Main Collections
- **DeviceData**: Main collection for device-related data
- **FCMDetails**: Firebase Cloud Messaging tokens
- **GoogleHealthData/AppleHealthData**: Platform-specific health data

### Key Database Operations

#### Batch Upload Operations
**File**: `lib/services/firestore_service.dart`
- `uploadBatchDataV2()`: Handles bulk data uploads with existence checks
- Uses WriteBatch for atomic operations
- Implements duplicate prevention logic
- Supports GT-Plus specific data operations

#### Database Best Practices
1. Always use batch operations for multiple writes
2. Check document existence before writing to prevent duplicates
3. Include timestamp fields for data tracking
4. Use proper error handling and logging
5. Implement data validation before storage

## 2. Bucket Operations (Google Cloud Storage)

### Bucket Configuration
**File**: `lib/services/gcs_upload_service.dart`

#### Environment-Based Bucket Selection
```dart
// Main data bucket
static String getBucketName() {
  return isProdEnv ? 'saiwell-gtplus' : 'saiwell-gtplus-staging';
}

// Voice recordings bucket  
static String getVoiceBucketName() {
  return isProdEnv ? 'speech_data_bucket' : 'hydra_test_sh';
}
```

#### GT-Plus Specific Logic
```dart
static String getVoiceBucketNameForGtPlus({bool isFromGtPlus = false}) {
  if (isFromGtPlus) {
    return getBucketName(); // Use main bucket for GT-Plus recordings
  } else {
    return getVoiceBucketName(); // Use dedicated voice bucket for others
  }
}
```

### File Naming Conventions

#### For GT-Plus Recordings
- **Speech**: `{timestamp}_{platform}_{version}_speech.wav`
- **PPG Data**: `{timestamp}_{platform}_{version}_ppgWave.csv`
- **Face Data**: `{timestamp}_{platform}_{version}_facedata.csv` (Android) / `{timestamp}_{platform}_{version}_facemesh.csv` (iOS)

#### For Regular Recordings
- **Speech**: `{uid}_{timestamp}.wav`
- **Other Data**: `{timestamp}_{platform}_{version}_{type}.{extension}`

### Upload Process
1. **Data Preparation**: Convert data to appropriate format (CSV for metrics, WAV for audio)
2. **Path Generation**: Build GCS path using naming conventions
3. **Authentication**: Use service account credentials from `assets/gCloud/credentials.json`
4. **Upload**: Stream data to GCS with proper content type
5. **Verification**: Check upload success and handle errors

### Supported Data Types
- **PPG Wave Data**: CSV format with timestamp and amplitude values
- **Voice Recordings**: WAV format audio files
- **Face Mesh Data**: CSV format with facial landmark coordinates
- **Camera Images**: JPEG/PNG format images

## 3. PPG Recording Logic

### PPG Controller
**File**: `lib/modules/ppg/controller/ppg_controller.dart`

#### Key Components
- **Data Collection**: Real-time PPG signal processing
- **Platform Differences**: iOS vs Android processing logic
- **Data Storage**: Local CSV generation and GCS upload
- **UI State Management**: Recording progress, completion status

#### PPG Recording Flow
1. **Initialization**
   ```dart
   Future<bool> startPPGWave() async {
     ppgData.clear();
     allPPGData.clear();
     completionPercentage.value = 0.0;
     isRecording.value = true;
     await _channel?.invokeMethod('startPPGWaveV2');
   }
   ```

2. **Data Processing**
   - **iOS**: Advanced sampling and scaling with noise reduction
   - **Android**: Simple inversion processing (`data * -1`)
   - Real-time data visualization updates

3. **Data Storage**
   ```dart
   Future<bool> stopPPGWave() async {
     isRecording.value = false;
     await _channel?.invokeMethod('stopPPGWaveV2');
     await saveDataToCSV(); // Local storage
     return !isRecording.value;
   }
   ```

4. **Data Upload**
   ```dart
   Future<bool> submitPPGData() async {
     bool uploadSuccess = await uploadPPGDataToGCS();
     resetState();
     return uploadSuccess;
   }
   ```

#### Platform-Specific Implementation
- **Android**: `MainActivity.java` - BLE SDK integration for PPG data collection
- **iOS**: `ServiceBleiOS.swift` - SDK integration

#### Data Format
- **CSV Structure**: Timestamp, PPG amplitude values
- **Sampling Rate**: Configurable based on platform requirements
- **Data Validation**: Minimum data requirements before submission

## 4. GT-Plus Integration Logic

### GT-Plus Event Handling
**File**: `lib/modules/home/<USER>/home_controller.dart`

#### Key GT-Plus Events
1. **PPG Recording**: Handled via `ppgRecorded` event
   ```dart
   if (item == "ppgRecorded") {
     wasOnPPG = true;
     isFromGTPlus = true;
     debugPrint("🏷️ [GT-Plus] Set flag for PPG recording");
     // Navigate to PPG recording screen
   }
   ```

2. **Voice Recording**: GT-Plus voice recording integration
3. **Face Detection**: GT-Plus face mesh detection integration

### GT-Plus Flag Management
- **isFromGTPlus**: Global flag to track GT-Plus source
- **wasOnPPG**: Specific flag for PPG section tracking
- **Bucket Selection**: Determines correct GCS bucket based on source

## 5. Native Platform Communication

### Android Implementation
**File**: `android/app/src/main/java/com/saigeware/sh/saiwell/MainActivity.java`

#### Key Android Components
1. **Method Channel Setup**: Communication bridge between Flutter and native Android
2. **Battery Optimization**: Handles Android power management for background operations
3. **Permission Management**: Bluetooth and location permissions

#### Method Channel Operations
```java
// Main communication channel
private static final String CHANNEL = "com.saiwell.sw/android_native";

// Key methods for GT-Plus
- startPPGWaveV2()
- stopPPGWaveV2()
- handleCameraOperations()
```

### iOS Implementation
**File**: `ios/Runner/ServiceBleiOS.swift`

#### Key iOS Components
1. **Method Channel Setup**: Flutter-iOS communication bridge
2. **Permission Handling**: iOS-specific permission management
3. **Background Processing**: iOS background task management

#### Method Channel Operations
```swift
// Communication channel
private static let CHANNEL = "com.saiwell.sw/ios_native"

// Key operations for GT-Plus
- startPPGRecording()
- stopPPGRecording()
- handleFaceDetection()
```

## 6. Permission Management

### Bluetooth Service
**File**: `lib/services/bluetooth_service.dart`

#### Permission Handling
```dart
Future<bool> checkPermission() async {
  if (Platform.isIOS) {
    return true; // iOS doesn't need explicit Bluetooth permissions
  } else {
    // Android requires multiple permissions
    final permissionsGranted = await permissionUtil.checkMultiplePermissions([
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.locationWhenInUse,
    ]);
    return permissionsGranted;
  }
}
```

#### Key Features
- **Platform-Specific**: Different permission requirements for iOS/Android
- **User-Friendly**: Clear permission request messages
- **Settings Integration**: Direct navigation to app settings if needed

## 7. Voice Recording System

### Recording Controller
**File**: `lib/modules/recording/controller/playback_controller.dart`

#### GT-Plus Voice Logic
```dart
// Determine bucket based on source
final isFromGtPlus = _instructionController.isFromGtPlus;
final bucketName = GcsUploadService.getVoiceBucketNameForGtPlus(
  isFromGtPlus: isFromGtPlus,
);

// Upload with GT-Plus specific naming
final success = await GcsUploadService.uploadToGcs(
  bytes: bytes,
  uid: uid.isEmpty ? 'anonymous' : uid,
  type: 'speech',
  timestamp: timestamp,
  contentType: 'audio/wav',
  extension: 'wav',
  credentialsAssetPath: 'assets/gCloud/credentials.json',
  bucketName: bucketName,
  isFromGtPlus: isFromGtPlus,
);
```

#### File Naming Convention
- **GT-Plus**: `{timestamp}_{platform}_{version}_speech.wav`
- **Regular**: `{uid}_{timestamp}.wav`

### Audio Processing
- **Format**: WAV format for compatibility
- **Quality**: Configurable bitrate and sample rate
- **Compression**: Optimized for upload size vs quality

## 8. Face Mesh Detection

### Implementation Files
- **Android**: `lib/modules/faceMesh/controller/face_detection_android_controller.dart`
- **iOS**: `lib/modules/faceMesh/controller/face_detection_ios_controller.dart`

#### Data Processing
- **Face Landmark Detection**: Google ML Kit integration
- **CSV Export**: Structured facial coordinate data
- **Platform Differences**: Android uses 'facedata', iOS uses 'facemesh'
- **GCS Upload**: Direct upload to Google Cloud Storage

#### File Naming Convention
- **Android**: `{timestamp}_{platform}_{version}_facedata.csv`
- **iOS**: `{timestamp}_{platform}_{version}_facemesh.csv`

#### Upload Process
```dart
Future<bool> uploadFaceMeshDataToGCS({
  required String csvData,
  required String uid,
  required String type,
  required int timestamp,
}) async {
  final success = await GcsUploadService.uploadToGcs(
    bytes: utf8.encode(csvData),
    uid: uid,
    type: type,
    timestamp: timestamp,
    contentType: 'text/csv',
    extension: 'csv',
    credentialsAssetPath: 'assets/gCloud/credentials.json',
  );
  return success;
}
```

## 9. Configuration Management

### Environment Configuration
**File**: `lib/constants/constant.dart`

```dart
const bool isProdEnv =
    String.fromEnvironment("ENVIRONMENT", defaultValue: "development") == "production";
const String? fbDatabaseId = isProdEnv ? null : "staging";
```





