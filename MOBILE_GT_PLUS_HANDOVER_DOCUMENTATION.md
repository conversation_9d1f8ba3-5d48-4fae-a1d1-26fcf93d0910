# Mobile GT+ Handover Documentation

## Project Overview
Mobile GT+ is a Flutter-based health monitoring application that integrates with smart ring devices to collect and analyze various health metrics including PPG (Photoplethysmography), temperature, heart rate variability (HRV), blood oxygen, sleep data, and step counts.

## 1. Database Operations

### Firestore Collections Structure

#### V2 Collections (Current Implementation)
```dart
enum V2CollectionNames {
  saiwellRingTemperatureV2("saiwellRingTemperatureV2"),
  saiwellRingBloodOxygenV2("saiwellRingBloodOxygenV2"), 
  saiwellRingHrvV2("saiwellRingHrvV2"),
  saiwellRingSleepV2("saiwellRingSleepV2"),
  saiwellRingStepV2("saiwellRingStepV2")
}
```

#### Main Collections
- **DeviceData**: Main collection for device-related data
- **FCMDetails**: Firebase Cloud Messaging tokens
- **GoogleHealthData/AppleHealthData**: Platform-specific health data

### Key Database Operations

#### Batch Upload Operations
**File**: `lib/services/firestore_service.dart`
- `uploadBatchDataV2()`: Handles bulk data uploads with existence checks
- Uses WriteBatch for atomic operations
- Implements duplicate prevention logic
- Supports all health data types (temperature, HRV, oxygen, sleep, steps)

#### Data Models
- **TemperatureReadingModelV2**: Temperature data with date/time
- **HrvReadingModelV2**: Heart rate variability data
- **OxygenReadingModelV2**: Blood oxygen measurements
- **SleepReadingModelV2**: Sleep tracking data
- **StepsReadingModelV2**: Step count and activity data

#### Database Best Practices
1. Always use batch operations for multiple writes
2. Check document existence before writing to prevent duplicates
3. Include timestamp fields for data tracking
4. Use proper error handling and logging
5. Implement data validation before storage

## 2. Bucket Operations (Google Cloud Storage)

### Bucket Configuration
**File**: `lib/services/gcs_upload_service.dart`

#### Environment-Based Bucket Selection
```dart
// Main data bucket
static String getBucketName() {
  return isProdEnv ? 'saiwell-gtplus' : 'saiwell-gtplus-staging';
}

// Voice recordings bucket  
static String getVoiceBucketName() {
  return isProdEnv ? 'speech_data_bucket' : 'hydra_test_sh';
}
```

#### GT-Plus Specific Logic
```dart
static String getVoiceBucketNameForGtPlus({bool isFromGtPlus = false}) {
  if (isFromGtPlus) {
    return getBucketName(); // Use main bucket for GT-Plus recordings
  } else {
    return getVoiceBucketName(); // Use dedicated voice bucket for others
  }
}
```

### File Naming Conventions

#### For GT-Plus Recordings
- **Speech**: `{timestamp}_{platform}_{version}_speech.wav`
- **PPG Data**: `{timestamp}_{platform}_{version}_ppgWave.csv`
- **Face Data**: `{timestamp}_{platform}_{version}_facedata.csv` (Android) / `{timestamp}_{platform}_{version}_facemesh.csv` (iOS)

#### For Regular Recordings
- **Speech**: `{uid}_{timestamp}.wav`
- **Other Data**: `{timestamp}_{platform}_{version}_{type}.{extension}`

### Upload Process
1. **Data Preparation**: Convert data to appropriate format (CSV for metrics, WAV for audio)
2. **Path Generation**: Build GCS path using naming conventions
3. **Authentication**: Use service account credentials from `assets/gCloud/credentials.json`
4. **Upload**: Stream data to GCS with proper content type
5. **Verification**: Check upload success and handle errors

### Supported Data Types
- **PPG Wave Data**: CSV format with timestamp and amplitude values
- **Voice Recordings**: WAV format audio files
- **Face Mesh Data**: CSV format with facial landmark coordinates
- **Camera Images**: JPEG/PNG format images

## 3. PPG Recording Logic

### PPG Controller
**File**: `lib/modules/ppg/controller/ppg_controller.dart`

#### Key Components
- **Data Collection**: Real-time PPG signal processing
- **Platform Differences**: iOS vs Android processing logic
- **Data Storage**: Local CSV generation and GCS upload
- **UI State Management**: Recording progress, completion status

#### PPG Recording Flow
1. **Initialization**
   ```dart
   Future<bool> startPPGWave() async {
     ppgData.clear();
     allPPGData.clear();
     completionPercentage.value = 0.0;
     isRecording.value = true;
     await _channel?.invokeMethod('startPPGWaveV2');
   }
   ```

2. **Data Processing**
   - **iOS**: Advanced sampling and scaling with noise reduction
   - **Android**: Simple inversion processing (`data * -1`)
   - Real-time data visualization updates

3. **Data Storage**
   ```dart
   Future<bool> stopPPGWave() async {
     isRecording.value = false;
     await _channel?.invokeMethod('stopPPGWaveV2');
     await saveDataToCSV(); // Local storage
     return !isRecording.value;
   }
   ```

4. **Data Upload**
   ```dart
   Future<bool> submitPPGData() async {
     bool uploadSuccess = await uploadPPGDataToGCS();
     resetState();
     return uploadSuccess;
   }
   ```

#### Platform-Specific Implementation
- **Android**: `MainActivity.java` - BLE SDK integration for PPG data collection
- **iOS**: `ServiceBleiOS.swift` - YCProductSDK integration

#### Data Format
- **CSV Structure**: Timestamp, PPG amplitude values
- **Sampling Rate**: Configurable based on platform requirements
- **Data Validation**: Minimum data requirements before submission

## 4. Temperature Recording Logic

### Temperature Data Flow
**File**: `lib/services/native_communicator.dart`

#### Data Collection Process
1. **Native Method Call**
   ```dart
   Future<void> getTemperatureDataV2() async {
     final result = await _platform.invokeMethod('getTemperatureDataV2');
     List<TemperatureReadingModelV2> readings = 
         TemperatureReadingModelV2.parseReadings(result);
   }
   ```

2. **Data Parsing**
   - **Android Format**: JSON with `dicData` array
   - **iOS Format**: Bracketed string format
   - **Validation**: Date/time and temperature value validation

3. **Database Storage**
   ```dart
   bool isUpdated = await _firestoreService.uploadBatchDataV2(
     dataList: readings,
     uid: uid,
     collectionName: V2CollectionNames.saiwellRingTemperatureV2
   );
   ```

### Temperature Data Model
**File**: `lib/models/temperature_reading_model_v2.dart`

#### Key Features
- **Date Parsing**: Multiple format support
- **Temperature Conversion**: Celsius to Fahrenheit conversion
- **Error Handling**: Robust parsing with fallback mechanisms
- **Platform Compatibility**: Handles both Android and iOS data formats

#### Data Structure
```dart
class TemperatureReadingModelV2 {
  final DateTime date;
  final double temperature;
  
  // Conversion utility
  double convertToFahrenheit(num celsius) {
    return double.parse(((celsius * 1.8) + 32).toStringAsFixed(2));
  }
}
```

### GT-Plus Integration
- **Temperature Trigger**: Handled via `temperatureRecorded` event
- **Navigation**: Automatic routing to temperature measurement screen
- **Flag Management**: `isFromGTPlus` flag for source tracking

## 5. Native Platform Communication

### Android Implementation
**File**: `android/app/src/main/java/com/saigeware/sh/saiwell/MainActivity.java`

#### BLE Integration
- **SDK**: JStyle BLE SDK 2301 for ring device communication
- **Device Scanning**: Optimized 4-second scan with device filtering
- **Connection Management**: Auto-reconnection and connection state handling
- **Data Listeners**: Real-time data callbacks for health metrics

#### Key Android Components
1. **BleManager**: Device discovery and connection management
2. **BleService**: GATT operations and data communication
3. **DataListener2301**: Callback interface for health data
4. **Battery Optimization**: Handles Android power management

#### Method Channel Operations
```java
// Main communication channel
private static final String CHANNEL = "com.saiwell.sw/android_native";

// Key methods
- connectToDeviceV2(macId)
- getTemperatureDataV2()
- startPPGWaveV2()
- getBatteryLevelV2()
```

### iOS Implementation
**File**: `ios/Runner/ServiceBleiOS.swift`

#### BLE Integration
- **SDK**: YCProductSDK for ring device communication
- **Device Discovery**: 6-second scan with peripheral management
- **Connection Handling**: State-based connection management
- **Data Retrieval**: Structured data queries with completion handlers

#### Key iOS Components
1. **ServiceBle**: Main BLE service coordinator
2. **YCProduct**: SDK interface for device operations
3. **RingConnectivity**: Connection state management
4. **Data Models**: Structured response handling

#### Method Channel Operations
```swift
// Communication channel
private static let CHANNEL = "com.saiwell.sw/ios_native"

// Key operations
- scanForDevices()
- connectDevice(macId)
- getDeviceInfo()
- queryHealthData()
```

## 6. Ring Device Integration

### Device Connection Flow
1. **Permission Check**: Bluetooth and location permissions
2. **Device Scanning**: Platform-specific BLE scanning
3. **Device Selection**: User selects from discovered devices
4. **Connection**: Establish BLE GATT connection
5. **Authentication**: Device pairing and authentication
6. **Data Sync**: Fetch historical and real-time data

### Supported Health Metrics
- **Heart Rate Variability (HRV)**
- **Blood Oxygen Saturation (SpO2)**
- **Body Temperature**
- **Sleep Tracking**
- **Step Count and Activity**
- **PPG Waveform Data**

### Device Management
**File**: `lib/services/native_communicator.dart`

#### Connection Management
```dart
Future<void> connectToDeviceV2({
  required String macId,
  required void Function()? updateRingStatusToWebView,
  required void Function() reloadWebView,
  required void Function() updateDeviceConnectionDetailsToWebview,
  required void Function() updateBetteryStatusInWebView,
})
```

#### Data Synchronization
- **Batch Processing**: Efficient data transfer using queued operations
- **Duplicate Prevention**: Timestamp-based deduplication
- **Error Recovery**: Retry mechanisms for failed operations
- **Progress Tracking**: Real-time sync status updates

## 7. Voice Recording System

### Recording Controller
**File**: `lib/modules/recording/controller/playback_controller.dart`

#### GT-Plus Voice Logic
```dart
// Determine bucket based on source
final isFromGtPlus = _instructionController.isFromGtPlus;
final bucketName = GcsUploadService.getVoiceBucketNameForGtPlus(
  isFromGtPlus: isFromGtPlus,
);

// Upload with GT-Plus specific naming
final success = await GcsUploadService.uploadToGcs(
  bytes: bytes,
  uid: uid.isEmpty ? 'anonymous' : uid,
  type: 'speech',
  timestamp: timestamp,
  contentType: 'audio/wav',
  extension: 'wav',
  credentialsAssetPath: 'assets/gCloud/credentials.json',
  bucketName: bucketName,
  isFromGtPlus: isFromGtPlus,
);
```

#### File Naming Convention
- **GT-Plus**: `{timestamp}_{platform}_{version}_speech.wav`
- **Regular**: `{uid}_{timestamp}.wav`

### Audio Processing
- **Format**: WAV format for compatibility
- **Quality**: Configurable bitrate and sample rate
- **Compression**: Optimized for upload size vs quality

## 8. Face Mesh Detection

### Implementation Files
- **Android**: `lib/modules/faceMesh/controller/face_detection_android_controller.dart`
- **iOS**: `lib/modules/faceMesh/controller/face_detection_ios_controller.dart`

#### Data Processing
- **Face Landmark Detection**: Google ML Kit integration
- **CSV Export**: Structured facial coordinate data
- **Platform Differences**: Android uses 'facedata', iOS uses 'facemesh'

#### File Naming
- **Android**: `{timestamp}_{platform}_{version}_facedata.csv`
- **iOS**: `{timestamp}_{platform}_{version}_facemesh.csv`

## 9. Configuration Management

### Environment Configuration
**File**: `lib/constants/constant.dart`

```dart
const bool isProdEnv =
    String.fromEnvironment("ENVIRONMENT", defaultValue: "development") == "production";
const String? fbDatabaseId = isProdEnv ? null : "staging";
```

### Remote Configuration
**File**: `lib/services/firebase_remote_config_service.dart`

#### Key Configuration Keys
- `MIN_VERSION`: App version requirements
- `APPLE_HEALTH_TYPES`/`ANDROID_HEALTH_TYPES`: Platform health data types
- `VOICE_CONTENT`/`VOICE_INSTRUCTIONS`: Voice recording configurations
- `PPG_INSTRUCTIONS`: PPG recording parameters
- `NAVIGATION_PRODUCTION`/`NAVIGATION_STAGING`: Environment-specific navigation

## 10. Error Handling and Logging

### Logging Strategy
- **Debug Prints**: Development debugging with context tags
- **Error Logs**: Production error tracking with stack traces
- **Performance Logs**: Upload timing and data size tracking

### Error Recovery
- **Retry Mechanisms**: Automatic retry for failed operations
- **Fallback Strategies**: Alternative data sources when primary fails
- **User Feedback**: Clear error messages and recovery suggestions

## 11. Testing and Quality Assurance

### Testing Strategy
- **Unit Tests**: Core business logic and data models
- **Integration Tests**: Platform channel communication
- **Widget Tests**: UI component functionality
- **End-to-End Tests**: Complete user workflows

### Performance Considerations
- **Memory Management**: Proper disposal of controllers and streams
- **Battery Optimization**: Efficient BLE scanning and data processing
- **Network Efficiency**: Batch uploads and compression

## 12. Deployment and Maintenance

### Build Configuration
- **Environment Variables**: Production vs staging configuration
- **Code Signing**: Platform-specific signing requirements
- **Asset Management**: Proper credential file handling

### Monitoring
- **Firebase Analytics**: User behavior and app performance
- **Crashlytics**: Crash reporting and analysis
- **Remote Config**: Feature flag management

### Maintenance Tasks
- **Dependency Updates**: Regular SDK and package updates
- **Security Reviews**: Credential rotation and permission audits
- **Performance Optimization**: Regular profiling and optimization

## Key Developer Notes

1. **Always test on both platforms** - Android and iOS have different BLE implementations
2. **Handle GT-Plus flags carefully** - Bucket selection depends on source tracking
3. **Use batch operations** - Database writes should always use WriteBatch
4. **Validate data before upload** - Implement proper error checking
5. **Monitor upload sizes** - Large files may need compression or chunking
6. **Test offline scenarios** - App should handle network interruptions gracefully
7. **Follow naming conventions** - File naming is critical for data organization
8. **Keep credentials secure** - Never commit credential files to version control

## Contact Information
For technical questions or clarifications, refer to the codebase documentation and existing implementation patterns.
