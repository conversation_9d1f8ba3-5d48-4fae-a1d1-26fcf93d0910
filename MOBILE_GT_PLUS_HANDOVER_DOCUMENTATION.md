# Mobile GT+ Handover Documentation

## Project Overview
Mobile GT+ is a Flutter-based application that focuses on PPG (Photoplethysmography) recording, voice recording, face mesh detection, and data upload operations to Google Cloud Storage.

## 1. Database Operations

### Firestore Collections Structure

#### Main Collections
- **DeviceData**: Main collection for device-related data
- **FCMDetails**: Firebase Cloud Messaging tokens
- **GoogleHealthData/AppleHealthData**: Platform-specific health data

### Key Database Operations

#### Batch Upload Operations
**File**: `lib/services/firestore_service.dart`
- `uploadBatchDataV2()`: Handles bulk data uploads with existence checks
- Uses WriteBatch for atomic operations
- Implements duplicate prevention logic
- Supports GT-Plus specific data operations

#### Database Best Practices
1. Always use batch operations for multiple writes
2. Check document existence before writing to prevent duplicates
3. Include timestamp fields for data tracking
4. Use proper error handling and logging
5. Implement data validation before storage

## 2. Bucket Operations (Google Cloud Storage)

### Bucket Configuration
**File**: `lib/services/gcs_upload_service.dart`

#### Environment-Based Bucket Selection
```dart
// Main data bucket
static String getBucketName() {
  return isProdEnv ? 'saiwell-gtplus' : 'saiwell-gtplus-staging';
}

// Voice recordings bucket  
static String getVoiceBucketName() {
  return isProdEnv ? 'speech_data_bucket' : 'hydra_test_sh';
}
```

#### GT-Plus Specific Logic
```dart
static String getVoiceBucketNameForGtPlus({bool isFromGtPlus = false}) {
  if (isFromGtPlus) {
    return getBucketName(); // Use main bucket for GT-Plus recordings
  } else {
    return getVoiceBucketName(); // Use dedicated voice bucket for others
  }
}
```

### File Naming Conventions

#### For GT-Plus Recordings
- **Speech**: `{timestamp}_{platform}_{version}_speech.wav`
- **PPG Data**: `{timestamp}_{platform}_{version}_ppgWave.csv`
- **Face Data**: `{timestamp}_{platform}_{version}_facedata.csv` (Android) / `{timestamp}_{platform}_{version}_facemesh.csv` (iOS)

#### For Regular Recordings
- **Speech**: `{uid}_{timestamp}.wav`
- **Other Data**: `{timestamp}_{platform}_{version}_{type}.{extension}`

### Upload Process
1. **Data Preparation**: Convert data to appropriate format (CSV for metrics, WAV for audio)
2. **Path Generation**: Build GCS path using naming conventions
3. **Authentication**: Use service account credentials from `assets/gCloud/credentials.json`
4. **Upload**: Stream data to GCS with proper content type
5. **Verification**: Check upload success and handle errors

### Supported Data Types
- **PPG Wave Data**: CSV format with timestamp and amplitude values
- **Voice Recordings**: WAV format audio files
- **Face Mesh Data**: CSV format with facial landmark coordinates
- **Camera Images**: JPEG/PNG format images

## 3. PPG Recording Logic

### PPG Controller
**File**: `lib/modules/ppg/controller/ppg_controller.dart`

#### Key Components
- **Data Collection**: Real-time PPG signal processing
- **Platform Differences**: iOS vs Android processing logic
- **Data Storage**: Local CSV generation and GCS upload
- **UI State Management**: Recording progress, completion status

#### PPG Recording Flow
1. **Initialization**
   ```dart
   Future<bool> startPPGWave() async {
     ppgData.clear();
     allPPGData.clear();
     completionPercentage.value = 0.0;
     isRecording.value = true;
     await _channel?.invokeMethod('startPPGWaveV2');
   }
   ```

2. **Data Processing**
   - **iOS**: Advanced sampling and scaling with noise reduction
   - **Android**: Simple inversion processing (`data * -1`)
   - Real-time data visualization updates

3. **Data Storage**
   ```dart
   Future<bool> stopPPGWave() async {
     isRecording.value = false;
     await _channel?.invokeMethod('stopPPGWaveV2');
     await saveDataToCSV(); // Local storage
     return !isRecording.value;
   }
   ```

4. **Data Upload**
   ```dart
   Future<bool> submitPPGData() async {
     bool uploadSuccess = await uploadPPGDataToGCS();
     resetState();
     return uploadSuccess;
   }
   ```

#### Platform-Specific Implementation
- **Android**: `MainActivity.java` - BLE SDK integration for PPG data collection
- **iOS**: `ServiceBleiOS.swift` - SDK integration

#### Data Format
- **CSV Structure**: Timestamp, PPG amplitude values
- **Sampling Rate**: Configurable based on platform requirements
- **Data Validation**: Minimum data requirements before submission

## 4. GT-Plus Integration Logic

### GT-Plus Event Handling
**File**: `lib/modules/home/<USER>/home_controller.dart`

#### Key GT-Plus Events
1. **PPG Recording**: Handled via `ppgRecorded` event
   ```dart
   if (item == "ppgRecorded") {
     wasOnPPG = true;
     isFromGTPlus = true;
     debugPrint("🏷️ [GT-Plus] Set flag for PPG recording");
     // Navigate to PPG recording screen
   }
   ```

2. **Voice Recording**: GT-Plus voice recording integration
3. **Face Detection**: GT-Plus face mesh detection integration

### GT-Plus Flag Management
- **isFromGTPlus**: Global flag to track GT-Plus source
- **wasOnPPG**: Specific flag for PPG section tracking
- **Bucket Selection**: Determines correct GCS bucket based on source

## 5. Native Platform Communication

### Android Implementation
**File**: `android/app/src/main/java/com/saigeware/sh/saiwell/MainActivity.java`

#### Key Android Components
1. **Method Channel Setup**: Communication bridge between Flutter and native Android
2. **Battery Optimization**: Handles Android power management for background operations
3. **Permission Management**: Bluetooth and location permissions

#### Method Channel Operations
```java
// Main communication channel
private static final String CHANNEL = "com.saiwell.sw/android_native";

// Key methods for GT-Plus
- startPPGWaveV2()
- stopPPGWaveV2()
- handleCameraOperations()
```

### iOS Implementation
**File**: `ios/Runner/ServiceBleiOS.swift`

#### Key iOS Components
1. **Method Channel Setup**: Flutter-iOS communication bridge
2. **Permission Handling**: iOS-specific permission management
3. **Background Processing**: iOS background task management

#### Method Channel Operations
```swift
// Communication channel
private static let CHANNEL = "com.saiwell.sw/ios_native"

// Key operations for GT-Plus
- startPPGRecording()
- stopPPGRecording()
- handleFaceDetection()
```

## 6. Permission Management

### Bluetooth Service
**File**: `lib/services/bluetooth_service.dart`

#### Permission Handling
```dart
Future<bool> checkPermission() async {
  if (Platform.isIOS) {
    return true; // iOS doesn't need explicit Bluetooth permissions
  } else {
    // Android requires multiple permissions
    final permissionsGranted = await permissionUtil.checkMultiplePermissions([
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.locationWhenInUse,
    ]);
    return permissionsGranted;
  }
}
```

#### Key Features
- **Platform-Specific**: Different permission requirements for iOS/Android
- **User-Friendly**: Clear permission request messages
- **Settings Integration**: Direct navigation to app settings if needed

## 7. Voice Recording System

### Recording Controller
**File**: `lib/modules/recording/controller/playback_controller.dart`

#### GT-Plus Voice Logic
```dart
// Determine bucket based on source
final isFromGtPlus = _instructionController.isFromGtPlus;
final bucketName = GcsUploadService.getVoiceBucketNameForGtPlus(
  isFromGtPlus: isFromGtPlus,
);

// Upload with GT-Plus specific naming
final success = await GcsUploadService.uploadToGcs(
  bytes: bytes,
  uid: uid.isEmpty ? 'anonymous' : uid,
  type: 'speech',
  timestamp: timestamp,
  contentType: 'audio/wav',
  extension: 'wav',
  credentialsAssetPath: 'assets/gCloud/credentials.json',
  bucketName: bucketName,
  isFromGtPlus: isFromGtPlus,
);
```

#### File Naming Convention
- **GT-Plus**: `{timestamp}_{platform}_{version}_speech.wav`
- **Regular**: `{uid}_{timestamp}.wav`

### Audio Processing
- **Format**: WAV format for compatibility
- **Quality**: Configurable bitrate and sample rate
- **Compression**: Optimized for upload size vs quality

## 8. Face Mesh Detection

### Implementation Files
- **Android**: `lib/modules/faceMesh/controller/face_detection_android_controller.dart`
- **iOS**: `lib/modules/faceMesh/controller/face_detection_ios_controller.dart`

#### Data Processing
- **Face Landmark Detection**: Google ML Kit integration
- **CSV Export**: Structured facial coordinate data
- **Platform Differences**: Android uses 'facedata', iOS uses 'facemesh'
- **GCS Upload**: Direct upload to Google Cloud Storage

#### File Naming Convention
- **Android**: `{timestamp}_{platform}_{version}_facedata.csv`
- **iOS**: `{timestamp}_{platform}_{version}_facemesh.csv`

#### Upload Process
```dart
Future<bool> uploadFaceMeshDataToGCS({
  required String csvData,
  required String uid,
  required String type,
  required int timestamp,
}) async {
  final success = await GcsUploadService.uploadToGcs(
    bytes: utf8.encode(csvData),
    uid: uid,
    type: type,
    timestamp: timestamp,
    contentType: 'text/csv',
    extension: 'csv',
    credentialsAssetPath: 'assets/gCloud/credentials.json',
  );
  return success;
}
```

## 9. Configuration Management

### Environment Configuration
**File**: `lib/constants/constant.dart`

```dart
const bool isProdEnv =
    String.fromEnvironment("ENVIRONMENT", defaultValue: "development") == "production";
const String? fbDatabaseId = isProdEnv ? null : "staging";
```

## Frequently Asked Questions (Q&A)

### **Q1: Why do we have different bucket names for GT-Plus vs regular recordings?**
**A:** GT-Plus recordings are part of a specific research study and need to be stored separately for data analysis. Regular voice recordings go to a dedicated speech bucket (`hydra_test_sh` for staging, `speech_data_bucket` for production), while GT-Plus recordings go to the main GT-Plus bucket (`saiwell-gtplus-staging` or `saiwell-gtplus`).

```dart
// This logic ensures proper data segregation
static String getVoiceBucketNameForGtPlus({bool isFromGtPlus = false}) {
  if (isFromGtPlus) {
    return getBucketName(); // Main GT-Plus bucket
  } else {
    return getVoiceBucketName(); // Dedicated voice bucket
  }
}
```

### **Q2: What is the `isFromGTPlus` flag and why is it so important?**
**A:** The `isFromGTPlus` flag is a critical boolean that tracks whether the current recording session originated from the GT-Plus application. It affects:
- **Bucket selection** for file uploads
- **File naming conventions**
- **Data processing workflows**
- **Analytics tracking**

**Where it's set:**
```dart
// In home_controller.dart when GT-Plus events are received
if (item == "ppgRecorded") {
  isFromGTPlus = true; // Critical flag setting
  wasOnPPG = true;
}
```

### **Q3: Why do we use different file naming for GT-Plus recordings?**
**A:** GT-Plus requires specific metadata in filenames for research purposes:

**GT-Plus Format:** `{timestamp}_{platform}_{version}_speech.wav`
- Example: `1672531200000_android_3.6.5_speech.wav`

**Regular Format:** `{uid}_{timestamp}.wav`
- Example: `user123_1672531200000.wav`

This allows researchers to easily identify platform, app version, and timing without opening files.

### **Q4: What's the difference between PPG processing on iOS vs Android?**
**A:** The platforms handle PPG data differently due to hardware and SDK variations:

**iOS Processing:**
- Advanced sampling and scaling
- Noise reduction algorithms
- More sophisticated signal processing

**Android Processing:**
- Simple inversion (`data * -1`)
- Basic sampling rate control
- Straightforward data handling

```dart
if (Platform.isIOS) {
  final List<int> processedData = _processPPGDataForIOS(rawData);
  allPPGData.addAll(processedData);
} else {
  // Android: Simple inversion
  for (var data in rawData) {
    allPPGData.add((data as int) * -1);
  }
}
```

### **Q5: Why do we use WriteBatch for database operations?**
**A:** WriteBatch ensures **atomicity** - either all operations succeed or all fail. This prevents partial data corruption:

```dart
WriteBatch batch = firestore.batch();
// Add multiple operations
batch.set(docRef1, data1);
batch.set(docRef2, data2);
batch.set(docRef3, data3);
// All succeed or all fail
await batch.commit();
```

**Benefits:**
- Prevents inconsistent data states
- Better performance for multiple operations
- Reduces network calls
- Ensures data integrity

### **Q6: What is the purpose of the `_enqueue()` method in NativeCommunicator?**
**A:** The `_enqueue()` method ensures **sequential execution** of native operations to prevent race conditions and conflicts:

```dart
Future<void> getTemperatureDataV2() async {
  return _enqueue(() async {
    // This ensures only one operation runs at a time
    final result = await _platform.invokeMethod('getTemperatureDataV2');
    // Process result...
  });
}
```

**Why it's needed:**
- Native operations can conflict if run simultaneously
- Ensures proper order of execution
- Prevents data corruption from concurrent access

### **Q7: Why do we have platform-specific face detection naming (facedata vs facemesh)?**
**A:** Different platforms use different ML Kit implementations:

**Android:** Uses 'facedata' - refers to the structured face detection data
**iOS:** Uses 'facemesh' - refers to the face mesh detection capability

This naming helps distinguish the data source and processing method used.

### **Q8: What happens if a file upload fails?**
**A:** The system has multiple fallback mechanisms:

1. **Retry Logic**: Automatic retry for transient failures
2. **Local Storage**: Data is saved locally first, then uploaded
3. **Error Reporting**: Failed uploads are logged for debugging
4. **User Notification**: Users are informed of upload status

```dart
final success = await GcsUploadService.uploadToGcs(/* parameters */);
if (!success) {
  // Handle failure - retry, save locally, notify user
  print("Upload failed for $type");
  return false;
}
```

### **Q9: Why do we need both `wasOnPPG` and `isFromGTPlus` flags?**
**A:** These flags serve different purposes:

**`isFromGTPlus`**: Global flag indicating the session originated from GT-Plus
**`wasOnPPG`**: Specific flag indicating the user was in the PPG recording section

This dual-flag system allows for:
- Proper navigation handling
- Correct data processing workflows
- Accurate analytics tracking
- Proper cleanup when leaving sections

### **Q10: What is the significance of the timestamp format used in file names?**
**A:** We use `DateTime.now().millisecondsSinceEpoch` which provides:
- **Uniqueness**: No two files will have the same timestamp
- **Sortability**: Files are naturally sorted by creation time
- **Cross-platform compatibility**: Works identically on iOS and Android
- **Research requirements**: Precise timing for data analysis

### **Q11: Why do we convert PPG data to CSV format?**
**A:** CSV format is chosen because:
- **Universal compatibility**: Can be opened in Excel, Python, R, etc.
- **Human readable**: Easy to inspect and debug
- **Research friendly**: Standard format for data analysis
- **Lightweight**: Efficient storage and transfer
- **Version control friendly**: Text-based format for tracking changes

### **Q12: How does the app handle network interruptions during uploads?**
**A:** The app implements several strategies:
1. **Local persistence**: Data is saved locally before upload attempts
2. **Background retry**: Failed uploads are retried when network returns
3. **Progress tracking**: Users can see upload status
4. **Graceful degradation**: App continues to function offline

### **Q13: What are the security considerations for credential management?**
**A:** Critical security practices:
- **Asset storage**: Credentials stored in `assets/gCloud/credentials.json`
- **Environment separation**: Different credentials for staging vs production
- **No version control**: Credential files should never be committed
- **Minimal permissions**: Service accounts have only necessary permissions
- **Regular rotation**: Credentials should be rotated periodically

### **Q14: Why do we use Method Channels instead of direct plugin integration?**
**A:** Method Channels provide:
- **Custom native functionality**: Access to platform-specific SDKs and APIs
- **Real-time communication**: Bidirectional communication between Flutter and native
- **Performance**: Direct native code execution for intensive operations
- **Flexibility**: Custom implementation without plugin limitations

```dart
// Flutter side
await _platform.invokeMethod('startPPGWaveV2');

// Native side handles the actual hardware communication
```

### **Q15: What is the purpose of the completion percentage in PPG recording?**
**A:** The completion percentage serves multiple purposes:
- **User feedback**: Shows recording progress to keep users engaged
- **Data quality**: Ensures minimum recording duration for valid data
- **UI state management**: Controls when submit button becomes available
- **Analytics**: Tracks user engagement and completion rates

```dart
completionPercentage.value = (elapsedSeconds / totalRequiredSeconds) * 100;
canSubmit.value = completionPercentage.value >= 100;
```

### **Q16: How does the app determine which environment (staging vs production) to use?**
**A:** Environment determination uses compile-time constants:

```dart
const bool isProdEnv =
    String.fromEnvironment("ENVIRONMENT", defaultValue: "development") == "production";
```

**Build commands:**
- **Staging**: `flutter build --dart-define=ENVIRONMENT=development`
- **Production**: `flutter build --dart-define=ENVIRONMENT=production`

This affects:
- Database selection (staging vs production Firestore)
- Bucket names for file uploads
- API endpoints
- Feature flags

### **Q17: Why do we need the `needsFullRefresh` flag in PPG controller?**
**A:** This flag optimizes UI performance by controlling when the entire PPG graph needs to be redrawn:

```dart
needsFullRefresh.value = true; // Force complete redraw
// vs
needsFullRefresh.value = false; // Incremental updates only
```

**When full refresh is needed:**
- Starting new recording
- Clearing existing data
- Changing display settings
- Error recovery

### **Q18: What happens to locally saved data if the app crashes during upload?**
**A:** The app implements crash recovery:
1. **Local persistence**: Data is saved to device storage before upload
2. **Upload retry**: On next app launch, pending uploads are detected
3. **Data integrity**: Local files are validated before retry attempts
4. **Cleanup**: Successfully uploaded files are removed from local storage

### **Q19: How does the app handle concurrent PPG and voice recordings?**
**A:** The app prevents conflicts through:
- **State management**: Global flags prevent simultaneous recordings
- **Resource locking**: Native layer ensures exclusive access to hardware
- **UI blocking**: Recording screens disable navigation during active sessions
- **Error handling**: Clear error messages if conflicts occur

### **Q20: Why is the CSV data conversion done on the Flutter side instead of native?**
**A:** Flutter-side conversion provides:
- **Consistency**: Same CSV format across iOS and Android
- **Debugging**: Easier to inspect and modify CSV structure
- **Flexibility**: Can easily change format without native code changes
- **Testing**: Easier to unit test CSV generation logic
- **Maintenance**: Single codebase for data formatting

## Contact Information
For technical questions or clarifications, refer to the codebase documentation and existing implementation patterns.